# modules/cloud-run/outputs.tf
output "service_name" {
  description = "The name of the Cloud Run service"
  value       = google_cloud_run_v2_service.main.name
}

output "service_url" {
  description = "The URL of the Cloud Run service"
  value       = google_cloud_run_v2_service.main.uri
}

output "service_id" {
  description = "The ID of the Cloud Run service"
  value       = google_cloud_run_v2_service.main.id
}

output "service_account_email" {
  description = "Service account email used by Cloud Run"
  value       = google_service_account.cloud_run.email
}

output "latest_revision" {
  description = "Latest revision name"
  value       = google_cloud_run_v2_service.main.latest_created_revision
}

output "deployment_config" {
  description = "Configuration for GitHub Actions deployment"
  value = {
    service_name    = google_cloud_run_v2_service.main.name
    region          = var.region
    service_account = google_service_account.cloud_run.email
    note            = "Deploy new images with: gcloud run deploy ${google_cloud_run_v2_service.main.name} --image=NEW_IMAGE --region=${var.region}"
  }
}