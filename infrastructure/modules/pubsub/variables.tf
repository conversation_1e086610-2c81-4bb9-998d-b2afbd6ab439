# modules/pubsub/variables.tf
variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "region" {
  description = "GCP Region"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "topics" {
  description = "Map of topic names and their configurations"
  type = map(object({
    description = string
  }))
  default = {}
}