# modules/pubsub/main.tf

# Pub/Sub Topics
resource "google_pubsub_topic" "topics" {
  for_each = var.topics

  name = "${var.environment}-${each.key}"

  labels = {
    environment = var.environment
    managed_by  = "terraform"
  }

  # Message retention duration
  message_retention_duration = "86400s"  # 24 hours
}

# Pub/Sub Subscriptions
resource "google_pubsub_subscription" "subscriptions" {
  for_each = var.topics

  name  = "${var.environment}-${each.key}-subscription"
  topic = google_pubsub_topic.topics[each.key].name

  # Subscription configuration
  ack_deadline_seconds = 20
  message_retention_duration = "604800s"  # 7 days

  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "600s"
  }

  dead_letter_policy {
    dead_letter_topic     = google_pubsub_topic.dead_letter[each.key].id
    max_delivery_attempts = 5
  }

  labels = {
    environment = var.environment
    managed_by  = "terraform"
  }
}

# Dead letter topics for failed messages
resource "google_pubsub_topic" "dead_letter" {
  for_each = var.topics

  name = "${var.environment}-${each.key}-dead-letter"

  labels = {
    environment = var.environment
    managed_by  = "terraform"
    type        = "dead-letter"
  }

  message_retention_duration = "2592000s"  # 30 days
}