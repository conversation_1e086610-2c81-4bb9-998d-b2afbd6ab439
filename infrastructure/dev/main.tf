# main.tf
provider "google" {
  project = var.project_id
  region  = var.region
}

provider "google-beta" {
  project = var.project_id
  region  = var.region
}

# Enable required APIs
resource "google_project_service" "apis" {
  for_each = toset([
    "compute.googleapis.com",
    "container.googleapis.com",
    "artifactregistry.googleapis.com",
    "run.googleapis.com",
    "sqladmin.googleapis.com",
    "servicenetworking.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "iam.googleapis.com",
    "iamcredentials.googleapis.com",
    "sts.googleapis.com",
    "pubsub.googleapis.com",
    "secretmanager.googleapis.com",
    "storage.googleapis.com",
  ])
  
  service            = each.value
  disable_on_destroy = false
}

# VPC Network
resource "google_compute_network" "main" {
  name                    = "${var.environment}-vpc"
  auto_create_subnetworks = false
  
  depends_on = [google_project_service.apis]
}

# Cloud Router for NAT
resource "google_compute_router" "main" {
  name    = "${var.environment}-router"
  network = google_compute_network.main.id
  region  = var.region
}

# Cloud NAT for outbound internet access
resource "google_compute_router_nat" "main" {
  name                               = "${var.environment}-nat"
  router                             = google_compute_router.main.name
  region                             = var.region
  nat_ip_allocate_option             = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
}

# Private Service Connection for CloudSQL
resource "google_compute_global_address" "private_ip" {
  name          = "${var.environment}-private-ip"
  purpose       = "VPC_PEERING"
  address_type  = "INTERNAL"
  prefix_length = 16
  network       = google_compute_network.main.id
}

resource "google_service_networking_connection" "private_vpc" {
  network                 = google_compute_network.main.id
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [google_compute_global_address.private_ip.name]
  
  depends_on = [google_project_service.apis]
}

# Artifact Registry Module
module "artifact_registry" {
  source = "../modules/artifact-registry"
  
  project_id   = var.project_id
  region       = var.region
  environment  = var.environment
  
  repositories = {
    containers = {
      format      = "DOCKER"
      description = "Docker container images"
    }
    npm = {
      format      = "NPM"
      description = "NPM packages"
    }
  }
  
  depends_on = [google_project_service.apis]
}

# Service accounts for Cloud Run services (created separately to avoid circular dependencies)
resource "google_service_account" "cerana_api" {
  account_id   = "${var.environment}-cerana-api-sa"
  display_name = "Cloud Run Service Account for cerana-api"
  description  = "Managed by Terraform"

  depends_on = [google_project_service.apis]
}

resource "google_service_account" "talent_api" {
  account_id   = "${var.environment}-talent-api-sa"
  display_name = "Cloud Run Service Account for talent-api"
  description  = "Managed by Terraform"

  depends_on = [google_project_service.apis]
}

# Grant necessary permissions to the cerana-api service account
resource "google_project_iam_member" "cerana_api_permissions" {
  for_each = toset([
    "roles/cloudsql.client",
    "roles/secretmanager.secretAccessor",
    "roles/storage.objectViewer",
  ])

  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.cerana_api.email}"
}

# Grant necessary permissions to the talent-api service account
resource "google_project_iam_member" "talent_api_permissions" {
  for_each = toset([
    "roles/cloudsql.client",
    "roles/secretmanager.secretAccessor",
    "roles/storage.objectAdmin",
    "roles/pubsub.publisher",
    "roles/pubsub.subscriber",
  ])

  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.talent_api.email}"
}

# Storage Module for GCS buckets
module "storage" {
  source = "../modules/storage"

  project_id   = var.project_id
  region       = var.region
  environment  = var.environment

  # Pass the talent-api service account email for bucket access
  service_account_email = google_service_account.talent_api.email

  buckets = {
    resumes = {
      location      = var.region
      storage_class = "STANDARD"
      description   = "Storage for resume files"
      versioning    = true
      lifecycle_rules = [
        {
          action = {
            type = "Delete"
          }
          condition = {
            age = 365  # Delete files older than 1 year
          }
        }
      ]
    }
  }

  depends_on = [
    google_project_service.apis,
    google_service_account.talent_api
  ]
}

# Secrets Module for sensitive environment variables
module "talent_api_secrets" {
  source = "../modules/secrets"

  project_id   = var.project_id
  environment  = var.environment

  # Pass the talent-api service account email for secret access
  service_account_email = google_service_account.talent_api.email

  secrets = {
    database-url = {
      description = "Database connection URL"
      # Will be constructed and set after CloudSQL is created
    }
    clerk-jwks-url = {
      description   = "Clerk JWKS URL for JWT verification"
      initial_value = "https://exact-horse-66.clerk.accounts.dev/.well-known/jwks.json"
    }
    clerk-issuer = {
      description   = "Clerk JWT issuer"
      initial_value = "https://exact-horse-66.clerk.accounts.dev"
    }
    clerk-audience = {
      description   = "Clerk JWT audience"
      initial_value = "https://cerana.netlify.app"
    }
    pubsub-verification-token = {
      description   = "Pub/Sub verification token"
      initial_value = "random-string-change-me"
    }
    openai-api-key = {
      description = "OpenAI API key"
      # No initial value - must be set manually for security
    }
  }

  depends_on = [
    google_project_service.apis,
    google_service_account.talent_api
  ]
}

# Set the DATABASE_URL secret after CloudSQL and secrets are created
resource "null_resource" "set_database_url" {
  triggers = {
    cloudsql_instance = module.cloudsql.instance_name
    secret_id         = module.talent_api_secrets.secret_ids["database-url"]
  }

  provisioner "local-exec" {
    command = <<-EOT
      # Get the database password from Secret Manager
      DB_PASSWORD=$(gcloud secrets versions access latest --secret="${module.cloudsql.password_secret_id}")

      # Construct the DATABASE_URL
      DATABASE_URL="postgresql+asyncpg://app_user:$${DB_PASSWORD}@${module.cloudsql.private_ip}:5432/${module.cloudsql.database_name}"

      # Set the DATABASE_URL secret
      echo -n "$DATABASE_URL" | gcloud secrets versions add "${module.talent_api_secrets.secret_ids["database-url"]}" --data-file=-
    EOT
  }

  depends_on = [
    module.cloudsql,
    module.talent_api_secrets
  ]
}

# CloudSQL Module
module "cloudsql" {
  source = "../modules/cloudsql"
  
  project_id    = var.project_id
  region        = var.region
  environment   = var.environment
  network_id    = google_compute_network.main.id
  
  database_version = "POSTGRES_15"
  tier            = var.cloudsql_tier
  
  depends_on = [
    google_project_service.apis,
    google_service_networking_connection.private_vpc
  ]
}

# Cloud Run Module
module "cloud_run" {
  source = "../modules/cloud-run"

  project_id   = var.project_id
  region       = var.region
  environment  = var.environment
  service_name = "cerana-api"  # Your service name

  # Use the existing service account
  existing_service_account_email = google_service_account.cerana_api.email

  # Initial image - GitHub Actions will update this
  initial_image = "gcr.io/cloudrun/hello"

  # VPC Configuration
  vpc_connector_name = google_vpc_access_connector.connector.id

  # Resources
  cpu    = "1"
  memory = "512Mi"

  # Scaling
  min_instances = 0
  max_instances = 100

  # CloudSQL connections
  cloudsql_connections = [module.cloudsql.connection_name]

  # Environment variables
  env_vars = [
    {
      name  = "PROJECT_ID"
      value = var.project_id
    },
    {
      name  = "ENVIRONMENT"
      value = var.environment
    },
    {
      name  = "DB_HOST"
      value = module.cloudsql.private_ip
    },
    {
      name  = "DB_NAME"
      value = module.cloudsql.database_name
    },
    {
      name  = "DB_USER"
      value = "app_user"
    }
  ]

  # Secret environment variables
  secret_env_vars = [
    {
      name        = "DB_PASSWORD"
      secret_name = module.cloudsql.password_secret_id
      version     = "1"
    }
  ]
  
  # IAM
  allow_unauthenticated = true

  # Allow GitHub Actions service accounts to impersonate this Cloud Run service account
  github_actions_service_accounts = [
    module.workload_identity.service_account_emails["cerana-api"]
  ]

  depends_on = [
    google_project_service.apis,
    google_vpc_access_connector.connector,
    google_service_account.cerana_api,
    google_project_iam_member.cerana_api_permissions,
    module.cloudsql,
  ]
}

# Talent API Cloud Run Module
module "talent_api_cloud_run" {
  source = "../modules/cloud-run"

  project_id   = var.project_id
  region       = var.region
  environment  = var.environment
  service_name = "talent-api"  # Talent API service name

  # Use the existing service account
  existing_service_account_email = google_service_account.talent_api.email

  # Initial image - GitHub Actions will update this
  initial_image = "gcr.io/cloudrun/hello"

  # VPC Configuration
  vpc_connector_name = google_vpc_access_connector.connector.id

  # Resources
  cpu    = "1"
  memory = "512Mi"

  # Scaling
  min_instances = 0
  max_instances = 100

  # CloudSQL connections
  cloudsql_connections = [module.cloudsql.connection_name]

  # Environment variables
  env_vars = [
    {
      name  = "ENVIRONMENT"
      value = "development"
    },
    {
      name  = "GCP_PROJECT_ID"
      value = var.project_id
    },
    {
      name  = "GCS_BUCKET_RESUMES"
      value = module.storage.bucket_names["resumes"]
    },
    {
      name  = "PUBSUB_TOPIC_MATCHES_COMPUTE"
      value = module.pubsub.topic_names["matches_compute"]
    },
    {
      name  = "PUBSUB_TOPIC_RESUME_UPLOADED"
      value = module.pubsub.topic_names["resume_uploaded"]
    },
    {
      name  = "PUBSUB_TOPIC_PROFILE_UPDATED"
      value = module.pubsub.topic_names["profile_updated"]
    },
    {
      name  = "EMBEDDINGS_PROVIDER"
      value = "openai"
    },
    {
      name  = "VECTOR_DIM"
      value = "1536"
    },
    {
      name  = "CORS_ALLOW_ORIGINS"
      value = "[\"*\"]"
    },
    {
      name  = "RATE_LIMIT_REQUESTS_PER_MINUTE"
      value = "60"
    },
    {
      name  = "MAX_RESUME_SIZE_MB"
      value = "25"
    },
    {
      name  = "MATCH_MIN_SKILL_OVERLAP"
      value = "1"
    },
    {
      name  = "MATCH_TOP_K_RESULTS"
      value = "50"
    },
    {
      name  = "LOG_LEVEL"
      value = "INFO"
    },
    {
      name  = "LOG_FORMAT"
      value = "json"
    }
  ]

  # Secret environment variables
  secret_env_vars = [
    {
      name        = "DATABASE_URL"
      secret_name = module.talent_api_secrets.secret_ids["database-url"]
      version     = "latest"
    },
    {
      name        = "CLERK_JWKS_URL"
      secret_name = module.talent_api_secrets.secret_ids["clerk-jwks-url"]
      version     = "latest"
    },
    {
      name        = "CLERK_ISSUER"
      secret_name = module.talent_api_secrets.secret_ids["clerk-issuer"]
      version     = "latest"
    },
    {
      name        = "CLERK_AUDIENCE"
      secret_name = module.talent_api_secrets.secret_ids["clerk-audience"]
      version     = "latest"
    },
    {
      name        = "PUBSUB_VERIFICATION_TOKEN"
      secret_name = module.talent_api_secrets.secret_ids["pubsub-verification-token"]
      version     = "latest"
    },
    {
      name        = "OPENAI_API_KEY"
      secret_name = module.talent_api_secrets.secret_ids["openai-api-key"]
      version     = "latest"
    }
  ]

  # IAM
  allow_unauthenticated = true

  # Allow GitHub Actions service accounts to impersonate this Cloud Run service account
  github_actions_service_accounts = [
    module.workload_identity.service_account_emails["talent-api"]
  ]

  depends_on = [
    google_project_service.apis,
    google_vpc_access_connector.connector,
    google_service_account.talent_api,
    google_project_iam_member.talent_api_permissions,
    module.cloudsql,
    module.pubsub,
    module.storage,
    module.talent_api_secrets,
  ]
}

# VPC Connector for Cloud Run
resource "google_vpc_access_connector" "connector" {
  name          = "${var.environment}-connector"
  region        = var.region
  network       = google_compute_network.main.id
  ip_cidr_range = "********/28"
}

# Pub/Sub Module for Talent API
module "pubsub" {
  source = "../modules/pubsub"
  
  project_id  = var.project_id
  region      = var.region
  environment = var.environment
  
  topics = {
    matches_compute = {
      description = "Topic for matches computation events"
      name        = "cerana.matches.compute.v1"
    }
    resume_uploaded = {
      description = "Topic for resume upload events"
      name        = "cerana.talent.resume_uploaded.v1"
    }
    profile_updated = {
      description = "Topic for profile update events"
      name        = "cerana.talent.profile_updated.v1"
    }
  }
  
  depends_on = [google_project_service.apis]
}

# Workload Identity Federation Module
module "workload_identity" {
  source = "../modules/workload-identity"

  project_id  = var.project_id
  environment = var.environment
  github_org  = var.github_org

  github_repositories = {
    infrastructure = {
      name = "infrastructure"
      roles = [
        "roles/artifactregistry.writer",
        "roles/run.developer",
        "roles/container.developer",
        "roles/storage.objectAdmin",
      ]
    }
    cerana-api = {
      name = "cerana-api"
      roles = [
        "roles/artifactregistry.writer",
        "roles/run.developer",
        "roles/container.developer",
        "roles/storage.objectAdmin",
      ]
    }
    talent-api = {
      name = "talent-api"
      roles = [
        "roles/artifactregistry.writer",
        "roles/run.developer",
        "roles/container.developer",
        "roles/storage.objectAdmin",
      ]
    }
  }

  depends_on = [google_project_service.apis]
}
