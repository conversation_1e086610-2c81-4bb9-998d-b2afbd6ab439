# main.tf
provider "google" {
  project = var.project_id
  region  = var.region
}

provider "google-beta" {
  project = var.project_id
  region  = var.region
}

# Enable required APIs
resource "google_project_service" "apis" {
  for_each = toset([
    "compute.googleapis.com",
    "container.googleapis.com",
    "artifactregistry.googleapis.com",
    "run.googleapis.com",
    "sqladmin.googleapis.com",
    "servicenetworking.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "iam.googleapis.com",
    "iamcredentials.googleapis.com",
    "sts.googleapis.com",
    "pubsub.googleapis.com",
    "secretmanager.googleapis.com",
    "storage.googleapis.com",
  ])
  
  service            = each.value
  disable_on_destroy = false
}

# VPC Network
resource "google_compute_network" "main" {
  name                    = "${var.environment}-vpc"
  auto_create_subnetworks = false
  
  depends_on = [google_project_service.apis]
}

# Cloud Router for NAT
resource "google_compute_router" "main" {
  name    = "${var.environment}-router"
  network = google_compute_network.main.id
  region  = var.region
}

# Cloud NAT for outbound internet access
resource "google_compute_router_nat" "main" {
  name                               = "${var.environment}-nat"
  router                             = google_compute_router.main.name
  region                             = var.region
  nat_ip_allocate_option             = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
}

# Private Service Connection for CloudSQL
resource "google_compute_global_address" "private_ip" {
  name          = "${var.environment}-private-ip"
  purpose       = "VPC_PEERING"
  address_type  = "INTERNAL"
  prefix_length = 16
  network       = google_compute_network.main.id
}

resource "google_service_networking_connection" "private_vpc" {
  network                 = google_compute_network.main.id
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [google_compute_global_address.private_ip.name]
  
  depends_on = [google_project_service.apis]
}

# Artifact Registry Module
module "artifact_registry" {
  source = "../modules/artifact-registry"
  
  project_id   = var.project_id
  region       = var.region
  environment  = var.environment
  
  repositories = {
    containers = {
      format      = "DOCKER"
      description = "Docker container images"
    }
    npm = {
      format      = "NPM"
      description = "NPM packages"
    }
  }
  
  depends_on = [google_project_service.apis]
}

# Storage Module for GCS buckets
module "storage" {
  source = "../modules/storage"

  project_id   = var.project_id
  region       = var.region
  environment  = var.environment

  # Pass the talent-api service account email for bucket access
  service_account_email = module.talent_api_cloud_run.service_account_email

  buckets = {
    resumes = {
      location      = var.region
      storage_class = "STANDARD"
      description   = "Storage for resume files"
      versioning    = true
      lifecycle_rules = [
        {
          action = {
            type = "Delete"
          }
          condition = {
            age = 365  # Delete files older than 1 year
          }
        }
      ]
    }
  }

  depends_on = [
    google_project_service.apis,
    module.talent_api_cloud_run
  ]
}

# Secrets Module for sensitive environment variables
module "talent_api_secrets" {
  source = "../modules/secrets"

  project_id   = var.project_id
  environment  = var.environment

  # Pass the talent-api service account email for secret access
  service_account_email = module.talent_api_cloud_run.service_account_email

  secrets = {
    clerk-jwks-url = {
      description   = "Clerk JWKS URL for JWT verification"
      initial_value = "https://exact-horse-66.clerk.accounts.dev/.well-known/jwks.json"
    }
    clerk-issuer = {
      description   = "Clerk JWT issuer"
      initial_value = "https://exact-horse-66.clerk.accounts.dev"
    }
    clerk-audience = {
      description   = "Clerk JWT audience"
      initial_value = "https://cerana.netlify.app"
    }
    pubsub-verification-token = {
      description   = "Pub/Sub verification token"
      initial_value = "random-string-change-me"
    }
    openai-api-key = {
      description = "OpenAI API key"
      # No initial value - must be set manually for security
    }
  }

  depends_on = [
    google_project_service.apis,
    module.talent_api_cloud_run
  ]
}

# CloudSQL Module
module "cloudsql" {
  source = "../modules/cloudsql"
  
  project_id    = var.project_id
  region        = var.region
  environment   = var.environment
  network_id    = google_compute_network.main.id
  
  database_version = "POSTGRES_15"
  tier            = var.cloudsql_tier
  
  depends_on = [
    google_project_service.apis,
    google_service_networking_connection.private_vpc
  ]
}

# Cloud Run Module
module "cloud_run" {
  source = "../modules/cloud-run"
  
  project_id   = var.project_id
  region       = var.region
  environment  = var.environment
  service_name = "cerana-api"  # Your service name
  
  # Initial image - GitHub Actions will update this
  initial_image = "gcr.io/cloudrun/hello"
  
  # VPC Configuration
  vpc_connector_name = google_vpc_access_connector.connector.id
  
  # Resources
  cpu    = "1"
  memory = "512Mi"
  
  # Scaling
  min_instances = 0
  max_instances = 100
  
  # CloudSQL connections
  cloudsql_connections = [module.cloudsql.connection_name]

  # Environment variables
  env_vars = [
    {
      name  = "PROJECT_ID"
      value = var.project_id
    },
    {
      name  = "ENVIRONMENT"
      value = var.environment
    },
    {
      name  = "DB_HOST"
      value = module.cloudsql.private_ip
    },
    {
      name  = "DB_NAME"
      value = module.cloudsql.database_name
    },
    {
      name  = "DB_USER"
      value = "app_user"
    }
  ]

  # Secret environment variables
  secret_env_vars = [
    {
      name        = "DB_PASSWORD"
      secret_name = module.cloudsql.password_secret_id
      version     = "1"
    }
  ]
  
  # IAM
  allow_unauthenticated = true
  
  service_account_roles = [
    "roles/cloudsql.client",
    "roles/secretmanager.secretAccessor",
    "roles/storage.objectViewer",
  ]

  # Allow GitHub Actions service accounts to impersonate this Cloud Run service account
  github_actions_service_accounts = [
    module.workload_identity.service_account_emails["cerana-api"]
  ]

  depends_on = [
    google_project_service.apis,
    google_vpc_access_connector.connector,
    module.cloudsql,
  ]
}

# Talent API Cloud Run Module
module "talent_api_cloud_run" {
  source = "../modules/cloud-run"

  project_id   = var.project_id
  region       = var.region
  environment  = var.environment
  service_name = "talent-api"  # Talent API service name

  # Initial image - GitHub Actions will update this
  initial_image = "gcr.io/cloudrun/hello"

  # VPC Configuration
  vpc_connector_name = google_vpc_access_connector.connector.id

  # Resources
  cpu    = "1"
  memory = "512Mi"

  # Scaling
  min_instances = 0
  max_instances = 100

  # CloudSQL connections
  cloudsql_connections = [module.cloudsql.connection_name]

  # Environment variables
  env_vars = [
    {
      name  = "GCP_PROJECT_ID"
      value = var.project_id
    },
    {
      name  = "ENVIRONMENT"
      value = var.environment
    },
    {
      name  = "DB_HOST"
      value = module.cloudsql.private_ip
    },
    {
      name  = "DB_NAME"
      value = module.cloudsql.database_name
    },
    {
      name  = "DB_USER"
      value = "app_user"
    },
    {
      name  = "DB_PORT"
      value = "5432"
    },
    {
      name  = "GCS_BUCKET_RESUMES"
      value = module.storage.bucket_names["resumes"]
    },
    {
      name  = "PUBSUB_TOPIC_MATCHES_COMPUTE"
      value = module.pubsub.topic_names["matches_compute"]
    },
    {
      name  = "PUBSUB_TOPIC_RESUME_UPLOADED"
      value = module.pubsub.topic_names["resume_uploaded"]
    },
    {
      name  = "PUBSUB_TOPIC_PROFILE_UPDATED"
      value = module.pubsub.topic_names["profile_updated"]
    }
  ]

  # Secret environment variables
  secret_env_vars = [
    {
      name        = "DB_PASSWORD"
      secret_name = module.cloudsql.password_secret_id
      version     = "1"
    },
    {
      name        = "CLERK_JWKS_URL"
      secret_name = module.talent_api_secrets.secret_ids["clerk-jwks-url"]
      version     = "latest"
    },
    {
      name        = "CLERK_ISSUER"
      secret_name = module.talent_api_secrets.secret_ids["clerk-issuer"]
      version     = "latest"
    },
    {
      name        = "CLERK_AUDIENCE"
      secret_name = module.talent_api_secrets.secret_ids["clerk-audience"]
      version     = "latest"
    },
    {
      name        = "PUBSUB_VERIFICATION_TOKEN"
      secret_name = module.talent_api_secrets.secret_ids["pubsub-verification-token"]
      version     = "latest"
    },
    {
      name        = "OPENAI_API_KEY"
      secret_name = module.talent_api_secrets.secret_ids["openai-api-key"]
      version     = "latest"
    }
  ]

  # IAM
  allow_unauthenticated = true

  service_account_roles = [
    "roles/cloudsql.client",
    "roles/secretmanager.secretAccessor",
    "roles/storage.objectAdmin",  # Updated to objectAdmin for resume uploads
    "roles/pubsub.publisher",
    "roles/pubsub.subscriber",
  ]

  # Allow GitHub Actions service accounts to impersonate this Cloud Run service account
  github_actions_service_accounts = [
    module.workload_identity.service_account_emails["talent-api"]
  ]

  depends_on = [
    google_project_service.apis,
    google_vpc_access_connector.connector,
    module.cloudsql,
    module.pubsub,
    module.storage,
    module.talent_api_secrets,
  ]
}

# VPC Connector for Cloud Run
resource "google_vpc_access_connector" "connector" {
  name          = "${var.environment}-connector"
  region        = var.region
  network       = google_compute_network.main.id
  ip_cidr_range = "********/28"
}

# Pub/Sub Module for Talent API
module "pubsub" {
  source = "../modules/pubsub"
  
  project_id  = var.project_id
  region      = var.region
  environment = var.environment
  
  topics = {
    matches_compute = {
      description = "Topic for matches computation events"
    }
    resume_uploaded = {
      description = "Topic for resume upload events"
    }
    profile_updated = {
      description = "Topic for profile update events"
    }
  }
  
  depends_on = [google_project_service.apis]
}

# Workload Identity Federation Module
module "workload_identity" {
  source = "../modules/workload-identity"

  project_id  = var.project_id
  environment = var.environment
  github_org  = var.github_org

  github_repositories = {
    infrastructure = {
      name = "infrastructure"
      roles = [
        "roles/artifactregistry.writer",
        "roles/run.developer",
        "roles/container.developer",
        "roles/storage.objectAdmin",
      ]
    }
    cerana-api = {
      name = "cerana-api"
      roles = [
        "roles/artifactregistry.writer",
        "roles/run.developer",
        "roles/container.developer",
        "roles/storage.objectAdmin",
      ]
    }
    talent-api = {
      name = "talent-api"
      roles = [
        "roles/artifactregistry.writer",
        "roles/run.developer",
        "roles/container.developer",
        "roles/storage.objectAdmin",
      ]
    }
  }

  depends_on = [google_project_service.apis]
}
