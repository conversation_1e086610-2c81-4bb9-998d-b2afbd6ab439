# Talent API Environment Variables

This document describes the environment variables configuration for the talent-api Cloud Run service.

## Overview

The talent-api service requires several environment variables for proper operation. These are managed through Terraform and include both regular environment variables and sensitive secrets stored in Google Secret Manager.

## Environment Variables

### Regular Environment Variables (managed by Terraform)

These are set directly in the Cloud Run service configuration:

- `GCP_PROJECT_ID`: The GCP project ID
- `ENVIRONMENT`: The deployment environment (e.g., "development", "staging", "production")
- `DB_HOST`: CloudSQL instance private IP address
- `DB_NAME`: Database name
- `DB_USER`: Database user (app_user)
- `DB_PORT`: Database port (5432)
- `GCS_BUCKET_RESUMES`: Name of the GCS bucket for storing resume files
- `PUBSUB_TOPIC_MATCHES_COMPUTE`: Pub/Sub topic for match computation
- `PUBSUB_TOPIC_RESUME_UPLOADED`: Pub/Sub topic for resume upload events
- `PUBSUB_TOPIC_PROFILE_UPDATED`: Pub/Sub topic for profile update events

### Secret Environment Variables (managed by Secret Manager)

These are stored securely in Google Secret Manager:

- `DB_PASSWORD`: Database password (auto-generated)
- `CLERK_JWKS_URL`: Clerk JWKS URL for JWT verification
- `CLERK_ISSUER`: Clerk JWT issuer
- `CLERK_AUDIENCE`: Clerk JWT audience
- `PUBSUB_VERIFICATION_TOKEN`: Pub/Sub verification token
- `OPENAI_API_KEY`: OpenAI API key (must be set manually)

## Database URL Construction

The application should construct the `DATABASE_URL` from the individual components:

```
DATABASE_URL = postgresql+asyncpg://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}
```

Example:
```
postgresql+asyncpg://app_user:password123@********:5432/main_db
```

## Setup Instructions

### 1. Deploy Infrastructure

```bash
cd infrastructure/dev
terraform init
terraform plan
terraform apply
```

### 2. Set the OpenAI API Key

The OpenAI API key must be set manually for security reasons:

```bash
# Get the secret name
terraform output talent_api_secret_ids

# Set the OpenAI API key
echo -n "your-openai-api-key-here" | gcloud secrets versions add main-openai-api-key --data-file=-
```

### 3. Update Other Secrets (if needed)

You can update any of the secrets using the gcloud CLI:

```bash
# Update Clerk JWKS URL
echo -n "https://your-clerk-instance.clerk.accounts.dev/.well-known/jwks.json" | \
  gcloud secrets versions add main-clerk-jwks-url --data-file=-

# Update Clerk Issuer
echo -n "https://your-clerk-instance.clerk.accounts.dev" | \
  gcloud secrets versions add main-clerk-issuer --data-file=-

# Update Clerk Audience
echo -n "https://your-app-domain.com" | \
  gcloud secrets versions add main-clerk-audience --data-file=-

# Update Pub/Sub verification token
echo -n "your-random-verification-token" | \
  gcloud secrets versions add main-pubsub-verification-token --data-file=-
```

## Verification

### Check Environment Variables

You can verify the environment variables are set correctly by checking the Cloud Run service:

```bash
gcloud run services describe talent-api --region=us-central1 --format="value(spec.template.spec.template.spec.containers[0].env[].name)"
```

### Check Secret Access

Verify that the service account has access to the secrets:

```bash
# Get the service account email
terraform output talent_api_cloud_run_service_account

# Check secret access
gcloud secrets get-iam-policy main-openai-api-key
```

## Troubleshooting

### Secret Access Issues

If the application can't access secrets:

1. Verify the service account has the `secretmanager.secretAccessor` role
2. Check that the secret exists and has a version
3. Ensure the secret name matches what's configured in Terraform

### Database Connection Issues

If database connection fails:

1. Verify the CloudSQL instance is running
2. Check that the VPC connector is properly configured
3. Ensure the database user and password are correct

### Storage Access Issues

If GCS bucket access fails:

1. Verify the service account has `storage.objectAdmin` role
2. Check that the bucket exists and is in the correct region
3. Ensure the bucket name is correctly set in the environment variable

## Security Notes

- Never commit API keys or passwords to version control
- Use Secret Manager for all sensitive configuration
- Regularly rotate API keys and tokens
- Monitor secret access logs for unauthorized usage
- Use least-privilege IAM roles for service accounts
